{
  "id": "github.com/yourname/agentchain",
  "consumes": ["application/json"],
  "produces": ["application/json"],
  "swagger": "2.0",
  "info":
    {
      "description": "Chain github.com/yourname/agentchain REST API",
      "title": "HTTP API Console",
      "contact": { "name": "github.com/yourname/agentchain" },
      "version": "version not set",
    },
  "paths": {},
  "definitions":
    {
      "google.protobuf.Any":
        {
          "type": "object",
          "properties": { "@type": { "type": "string" } },
          "additionalProperties": {},
        },
      "google.rpc.Status":
        {
          "type": "object",
          "properties":
            {
              "code": { "type": "integer", "format": "int32" },
              "details":
                {
                  "type": "array",
                  "items":
                    {
                      "type": "object",
                      "$ref": "#/definitions/google.protobuf.Any",
                    },
                },
              "message": { "type": "string" },
            },
        },
    },
}
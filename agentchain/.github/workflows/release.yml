# This workflow is useful if you want to automate the process of:
#
# a) Creating a new prelease when you push a new tag with a "v" prefix (version).
#
#    This type of prerelease is meant to be used for production: alpha, beta, rc, etc. types of releases.
#    After the prerelease is created, you need to make your changes on the release page at the relevant
#    Github page and publish your release.
#
# b) Creating/updating the "latest" prerelease when you push to your default branch.
#
#    This type of prelease is useful to make your bleeding-edge binaries available to advanced users.
#
# The workflow will not run if there is no tag pushed with a "v" prefix and no change pushed to your
# default branch.
on: push

jobs:
  might_release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Prepare Release Variables
        id: vars
        uses: ignite/cli/actions/release/vars@main

      - name: Issue Release Assets
        uses: ignite/cli/actions/cli@main
        if: ${{ steps.vars.outputs.should_release == 'true' }}
        with:
          args: chain build --release --release.prefix ${{ steps.vars.outputs.tarball_prefix }} -t linux:amd64 -t darwin:amd64 -t darwin:arm64 -y
        env:
          DO_NOT_TRACK: 1
          GOFLAGS: "-buildvcs=false"

      - name: Delete the "latest" Release
        uses: dev-drprasad/delete-tag-and-release@v0.2.1
        if: ${{ steps.vars.outputs.is_release_type_latest == 'true' }}
        with:
          tag_name: ${{ steps.vars.outputs.tag_name }}
          delete_release: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish the Release
        uses: softprops/action-gh-release@v1
        if: ${{ steps.vars.outputs.should_release == 'true' }}
        with:
          tag_name: ${{ steps.vars.outputs.tag_name }}
          files: release/*
          prerelease: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

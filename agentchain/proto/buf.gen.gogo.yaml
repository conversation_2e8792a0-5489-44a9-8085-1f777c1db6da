# This file is auto-generated from Ignite. You can edit
# the file content but do not change the file name or path.
#
# buf.gen.gogo.yaml
#
version: v2
plugins:
  - local: ["go", "tool", "github.com/cosmos/gogoproto/protoc-gen-gocosmos"]
    out: .
    opt:
      - plugins=grpc
      - Mgoogle/protobuf/any.proto=github.com/cosmos/gogoproto/types/any
      - Mcosmos/orm/v1/orm.proto=cosmossdk.io/orm
      - Mcosmos/app/v1alpha1/module.proto=cosmossdk.io/api/cosmos/app/v1alpha1
  - local:
      [
        "go",
        "tool",
        "github.com/grpc-ecosystem/grpc-gateway/protoc-gen-grpc-gateway",
      ]
    out: .
    opt:
      - logtostderr=true
      - allow_colon_final_segments=true

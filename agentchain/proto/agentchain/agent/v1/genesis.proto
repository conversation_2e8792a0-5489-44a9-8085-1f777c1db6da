syntax = "proto3";
package agentchain.agent.v1;

import "amino/amino.proto";
import "gogoproto/gogo.proto";
import "agentchain/agent/v1/params.proto";

option go_package = "agentchain/x/agent/types";

// GenesisState defines the agent module's genesis state.
message GenesisState {
  // params defines all the parameters of the module.
  Params params = 1 [
    (gogoproto.nullable) = false,
    (amino.dont_omitempty) = true
  ];
}

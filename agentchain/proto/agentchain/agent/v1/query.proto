syntax = "proto3";
package agentchain.agent.v1;

import "amino/amino.proto";
import "gogoproto/gogo.proto";
import "google/api/annotations.proto";
import "cosmos/base/query/v1beta1/pagination.proto";
import "agentchain/agent/v1/params.proto";

option go_package = "agentchain/x/agent/types";

// Query defines the gRPC querier service.
service Query {
  // Parameters queries the parameters of the module.
  rpc Params(QueryParamsRequest) returns (QueryParamsResponse) {
    option (google.api.http).get = "/agentchain/agent/v1/params";
  }
}

// QueryParamsRequest is request type for the Query/Params RPC method.
message QueryParamsRequest {}

// QueryParamsResponse is response type for the Query/Params RPC method.
message QueryParamsResponse {
  // params holds all the parameters of this module.
  Params params = 1 [
    (gogoproto.nullable) = false,
    (amino.dont_omitempty) = true
  ];
}
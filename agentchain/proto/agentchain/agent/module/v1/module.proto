syntax = "proto3";
package agentchain.agent.module.v1;

import "cosmos/app/v1alpha1/module.proto";

option go_package = "agentchain/x/agent/types";

// Module is the config object for the module.
message Module {
  option (cosmos.app.v1alpha1.module) = {
    go_import: "agentchain/x/agent"
  };

  // authority defines the custom module authority.
  // If not set, defaults to the governance module.
  string authority = 1;

  
}